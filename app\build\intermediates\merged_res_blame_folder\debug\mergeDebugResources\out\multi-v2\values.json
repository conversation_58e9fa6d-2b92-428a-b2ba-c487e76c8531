{"logs": [{"outputFile": "com.Hamode.periodpal.app-mergeDebugResources-63:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "6,7,8,9,48,49,177,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "412,470,536,599,2835,2906,11984,12420,12487,12566", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "465,531,594,656,2901,2973,12047,12482,12561,12630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "144,192,193,194,195,196,197,198,199,200,201,204,205,206,207,208,209,210,211,212,213,214,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,271,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8582,13267,13355,13441,13522,13606,13675,13740,13823,13929,14015,14135,14189,14258,14319,14388,14477,14572,14646,14743,14836,14934,15083,15174,15262,15358,15456,15520,15588,15675,15769,15836,15908,15980,16081,16190,16266,16335,16383,16449,16513,16587,16644,16701,16773,16823,16877,16948,17019,17089,17158,17216,17292,17363,17437,17523,17573,17643,18645,19360", "endLines": "144,192,193,194,195,196,197,198,199,200,203,204,205,206,207,208,209,210,211,212,213,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,280,283", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "8650,13350,13436,13517,13601,13670,13735,13818,13924,14010,14130,14184,14253,14314,14383,14472,14567,14641,14738,14831,14929,15078,15169,15257,15353,15451,15515,15583,15670,15764,15831,15903,15975,16076,16185,16261,16330,16378,16444,16508,16582,16639,16696,16768,16818,16872,16943,17014,17084,17153,17211,17287,17358,17432,17518,17568,17638,17703,19355,19508"}}, {"source": "D:\\PeriodPal\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,198,279,383,491,611,718", "endColumns": "142,80,103,107,119,106,75", "endOffsets": "193,274,378,486,606,713,789"}, "to": {"startLines": "180,185,186,187,188,189,255", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12219,12635,12716,12820,12928,13048,17824", "endColumns": "142,80,103,107,119,106,75", "endOffsets": "12357,12711,12815,12923,13043,13150,17895"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a6d0c3b56b429a19c2f930520d0c251b\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "6641", "endColumns": "49", "endOffsets": "6686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\11cdba141bdfb547960b7fcdc0b4367a\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "107,109,110,113,114,142,157,158,178,179,181,190,191,253,254,256,257,258,259,260,261,263,264,265,268,284,287", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6567,6691,6749,6927,6978,8461,9532,9597,12052,12118,12362,13155,13207,17708,17770,17900,17950,18004,18050,18096,18138,18249,18296,18332,18533,19513,19624", "endLines": "107,109,110,113,114,142,157,158,178,179,181,190,191,253,254,256,257,258,259,260,261,263,264,265,270,286,290", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "6636,6744,6799,6973,7028,8509,9592,9646,12113,12214,12415,13202,13262,17765,17819,17945,17999,18045,18091,18133,18173,18291,18327,18417,18640,19619,19814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2e988808bb7f73a8315e368f7c5f6e3\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,10,11,26,27,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,116,117,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,145,150,151,152,153,154,155,156,262,291,292,296,297,301,315,316,349,355,365,398,428,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,661,726,1746,1815,2978,3048,3116,3188,3258,3319,3393,3466,3527,3588,3650,3714,3776,3837,3905,4005,4065,4131,4204,4273,4330,4382,4444,4516,4592,4657,4716,4775,4835,4895,4955,5015,5075,5135,5195,5255,5315,5375,5434,5494,5554,5614,5674,5734,5794,5854,5914,5974,6034,6093,6153,6213,6272,6331,6390,6449,6508,7087,7122,7331,7386,7449,7504,7562,7620,7681,7744,7801,7852,7902,7963,8020,8086,8120,8155,8655,9021,9088,9160,9229,9298,9372,9444,18178,19819,19936,20137,20247,20448,21296,21368,22322,22525,22826,24557,25557,26239", "endLines": "2,3,4,10,11,26,27,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,116,117,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,145,150,151,152,153,154,155,156,262,291,295,296,300,301,315,316,354,364,397,418,460,466", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,721,787,1810,1873,3043,3111,3183,3253,3314,3388,3461,3522,3583,3645,3709,3771,3832,3900,4000,4060,4126,4199,4268,4325,4377,4439,4511,4587,4652,4711,4770,4830,4890,4950,5010,5070,5130,5190,5250,5310,5370,5429,5489,5549,5609,5669,5729,5789,5849,5909,5969,6029,6088,6148,6208,6267,6326,6385,6444,6503,6562,7117,7152,7381,7444,7499,7557,7615,7676,7739,7796,7847,7897,7958,8015,8081,8115,8150,8185,8720,9083,9155,9224,9293,9367,9439,9527,18244,19931,20132,20242,20443,20572,21363,21430,22520,22821,24552,25233,26234,26401"}}, {"source": "D:\\PeriodPal\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "46", "endOffsets": "58"}, "to": {"startLines": "149", "startColumns": "4", "startOffsets": "8974", "endColumns": "46", "endOffsets": "9016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a24524cd577336c01efc449f32e7672\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "317,333,339,499,515", "startColumns": "4,4,4,4,4", "startOffsets": "21435,21860,22038,27138,27549", "endLines": "332,338,348,514,518", "endColumns": "24,24,24,24,24", "endOffsets": "21855,22033,22317,27544,27671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bafe67932525e9d41a7253ce026a7fa1\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "307", "startColumns": "4", "startOffsets": "20886", "endLines": "314", "endColumns": "8", "endOffsets": "21291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e8b5bc1662136e087738bd938abfe3e7\\transformed\\credentials-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "146,147", "startColumns": "4,4", "startOffsets": "8725,8807", "endColumns": "81,83", "endOffsets": "8802,8886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b16198757d7398e736f933a6985e2a1b\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "112", "startColumns": "4", "startOffsets": "6861", "endColumns": "65", "endOffsets": "6922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2565267476792798ff0673441d513549\\transformed\\activity-1.9.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "119,138", "startColumns": "4,4", "startOffsets": "7224,8233", "endColumns": "41,59", "endOffsets": "7261,8288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e0ab38ddb181ba54137f5f1d540c94bd\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "8293", "endColumns": "53", "endOffsets": "8342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaef81146e2c476db006c571eca7f901\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "8190", "endColumns": "42", "endOffsets": "8228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "12,13,14,15,16,17,18,19,159,160,161,162,163,164,165,166,168,169,170,171,172,173,174,175,176,467,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,882,962,1052,1142,1222,1303,1383,9651,9756,9937,10062,10169,10349,10472,10588,10858,11046,11151,11332,11457,11632,11780,11843,11905,26406,26721", "endLines": "12,13,14,15,16,17,18,19,159,160,161,162,163,164,165,166,168,169,170,171,172,173,174,175,176,479,498", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "877,957,1047,1137,1217,1298,1378,1458,9751,9932,10057,10164,10344,10467,10583,10686,11041,11146,11327,11452,11627,11775,11838,11900,11979,26716,27133"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3a9e4472bfe093db10a5f3627a329c3c\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "140", "startColumns": "4", "startOffsets": "8347", "endColumns": "49", "endOffsets": "8392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1563c945d474f65ddd0bfe2b0b86275f\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "111,120,141,419,424", "startColumns": "4,4,4,4,4", "startOffsets": "6804,7266,8397,25238,25408", "endLines": "111,120,141,423,427", "endColumns": "56,64,63,24,24", "endOffsets": "6856,7326,8456,25403,25552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3163db060619b51a72cfb285e5abcba\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "143,167", "startColumns": "4,4", "startOffsets": "8514,10691", "endColumns": "67,166", "endOffsets": "8577,10853"}}, {"source": "D:\\PeriodPal\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "20,19,31,23,32,17,18,16,26,25,24,35,36,37,6,5,4,3,12,11,10,9,29,38,39,30,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,738,1205,881,1251,643,690,597,1025,978,931,1343,1390,1437,240,191,142,93,477,428,379,330,1104,1484,1529,1154,555", "endColumns": "41,45,45,49,45,46,47,45,50,46,46,46,46,46,48,48,48,48,48,48,48,48,49,44,44,50,41", "endOffsets": "821,779,1246,926,1292,685,733,638,1071,1020,973,1385,1432,1479,284,235,186,137,521,472,423,374,1149,1524,1569,1200,592"}, "to": {"startLines": "5,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,1463,1509,1555,1605,1651,1698,1878,1924,1975,2022,2069,2116,2163,2210,2259,2308,2357,2406,2455,2504,2553,2602,2652,2697,2742,2793", "endColumns": "41,45,45,49,45,46,47,45,50,46,46,46,46,46,48,48,48,48,48,48,48,48,49,44,44,50,41", "endOffsets": "407,1504,1550,1600,1646,1693,1741,1919,1970,2017,2064,2111,2158,2205,2254,2303,2352,2401,2450,2499,2548,2597,2647,2692,2737,2788,2830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c7e756cc255ba5643991ca2f73ae241a\\transformed\\credentials-play-services-auth-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "302", "startColumns": "4", "startOffsets": "20577", "endLines": "305", "endColumns": "12", "endOffsets": "20795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\738aced92cf0d3e841a31cacc78effce\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "115,118", "startColumns": "4,4", "startOffsets": "7033,7157", "endColumns": "53,66", "endOffsets": "7082,7219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3494e4d616dcc70de5ac033bfdba3623\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "148", "startColumns": "4", "startOffsets": "8891", "endColumns": "82", "endOffsets": "8969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9aad415eee13c5225ceb41d0c3631e4\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "266,267", "startColumns": "4,4", "startOffsets": "18422,18478", "endColumns": "55,54", "endOffsets": "18473,18528"}}, {"source": "D:\\PeriodPal\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "86", "endOffsets": "138"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "20800", "endColumns": "85", "endOffsets": "20881"}}]}]}