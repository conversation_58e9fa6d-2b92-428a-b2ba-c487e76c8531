-- Merging decision tree log ---
manifest
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:2:1-36:12
INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml:2:1-36:12
INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml:2:1-36:12
INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml:2:1-36:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3595878178060218ebb7af3bb6421cb0\transformed\googleid-1.1.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.credentials:credentials:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8b5bc1662136e087738bd938abfe3e7\transformed\credentials-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\462708bd37f95b6500e16ace9e164623\transformed\play-services-fido-21.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5a696deae0fe305f1bc165bd00d7684\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deeba7ce3690d9da9a220873c8ec344a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d517b2b91accbb1ee438acb3484a3ed3\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2064cea70af18bbf84d40fcf490cc9d9\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9766ed77526c3313f9626574fa94812\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e728e8aad9a69e3b607399bb48152ee5\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89b4dd4a95f1e943837f558e47297d3c\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f28034329ea35376057b553db42a06ad\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f6b594bfcf42b736f6f0ce22e1cffe1\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce33304cf3255238ef66c67378667420\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a1bc1d8f37d1a6b0194dab5384fc2c1\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c84a5c0fb8383dc487addc589f49bed\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\782a0a33dc02496be538bbc661aa65be\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6d0c3b56b429a19c2f930520d0c251b\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a4406fb22280fb302c67f992ef8133c\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\901c18f4ba81f5d50908c9f14f46b683\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4664de8a76f23c3e4e258538ed9b2b58\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc2b5ac2f6eb4aded5cdcf1e07acdfa0\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f78b3510f4a1f1bb0a0fdd93d3df1fb2\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5912c0c911551901b5253b16937b8db\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9aad415eee13c5225ceb41d0c3631e4\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1790f4c32bdc5aa4409d0db1320fb4f9\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cebcbb97b505907ae97ef9f6c573687\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d5f0b9b8602cbb854f5701accab68e5\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10298d9d40ee5303b78c26c7400a6920\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a0ed9c77a7dc9b64c33fef30fe58ae9\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b927026c75dd1bea752ac47a44a9de4\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16198757d7398e736f933a6985e2a1b\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3c7b3962208464e4594ebb813ca78c2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0ab38ddb181ba54137f5f1d540c94bd\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f3b0e192fae8d81415bbd16b5199dc2\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f88ef65c9bc8aa67d94848a25d26726\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68097891cc9b7c3f82e22792262965b7\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\956eabded315942ce5907cc63dc89883\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f318c1dabe4f575f887ee6cb92e13ff\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5faf63cc72e7771e60ff491c35840aa9\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167ac5f57d41a8d32d27023c0237b735\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a24524cd577336c01efc449f32e7672\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\738aced92cf0d3e841a31cacc78effce\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60f3277fff19b86fee9f41dd9f5a0653\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb57a2a816d23d18c0f2c4399312ffc\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23ccccae73f7acf66d4edaa95c868176\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14d0d29d49ac4289f3b9055537f4fe1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8d02c1e663e9fd2c30beea09851d0f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713c3110d19154ee2f68e6a7343e28a7\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aadb35340b4a827d4cbde408ff64707c\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f623f978dea677cef564465a0f62f1f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaef81146e2c476db006c571eca7f901\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08852f55135dfb4b7aba28a2fcf5d46c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa00fb55065a2f36fe2f24c84a21863b\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a9e4472bfe093db10a5f3627a329c3c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a898b8f2723d625fa8143324bd55bcb6\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30affbf2ce916a05fd0c246e50a84451\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca69d028d53405e396434bd1b33a8128\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55d4efc081c754d8273ac03850f13030\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78549bff33c42d531fe5485b4de5abb7\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cc29a7d02877d975ced36548d338d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1563c945d474f65ddd0bfe2b0b86275f\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54c0ace2928b6b83110c519f8b148f8c\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ba1333717fbdc0a7bcd58005c6dff50\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89db37d8cc333b1f09ff0e740265d432\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87978c6646afe0f4d2724bd00f50f64f\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97d602bc9d768a2fbf2c6ec5e195e55c\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0654f1c02991675061c1dbffd1cc19c\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11cdba141bdfb547960b7fcdc0b4367a\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2565267476792798ff0673441d513549\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daff2b002bbeb658aea41f8f7c906345\transformed\activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1e7285b5cf886f0c3fce641b2bea0f\transformed\activity-compose-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cecf361d2cc0bafaad723890a70f864\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21f60adbff86357bfc9c342c31a6827\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3494e4d616dcc70de5ac033bfdba3623\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad85ee529d468309743a11bec7acaf6d\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\812c1db28e1072341150d5f694a46e4e\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81de88cf9ba06a39513d4b7c0055feb7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c56bd892a6f8979b167a29abc8a282e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\481df57a7c0d63c31b36c7697e6872c9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7e96ef05eedf52fabeaf4d12b60c517\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c6c8aa082ab40d75baf759016dac52d\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6947564e97ca4142892836bb056425\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97bda15f39233236026bb7ae00346b6\transformed\grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddc599e35f50ee5d257f747f663c5956\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97bda15f39233236026bb7ae00346b6\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97bda15f39233236026bb7ae00346b6\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.CAMERA
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:8:5-9:38
	android:maxSdkVersion
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:10:5-76
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:10:22-73
application
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:12:5-34:19
INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml:12:5-34:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\462708bd37f95b6500e16ace9e164623\transformed\play-services-fido-21.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\462708bd37f95b6500e16ace9e164623\transformed\play-services-fido-21.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5a696deae0fe305f1bc165bd00d7684\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5a696deae0fe305f1bc165bd00d7684\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deeba7ce3690d9da9a220873c8ec344a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deeba7ce3690d9da9a220873c8ec344a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9766ed77526c3313f9626574fa94812\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9766ed77526c3313f9626574fa94812\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e728e8aad9a69e3b607399bb48152ee5\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e728e8aad9a69e3b607399bb48152ee5\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89b4dd4a95f1e943837f558e47297d3c\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89b4dd4a95f1e943837f558e47297d3c\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\956eabded315942ce5907cc63dc89883\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\956eabded315942ce5907cc63dc89883\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca69d028d53405e396434bd1b33a8128\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca69d028d53405e396434bd1b33a8128\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55d4efc081c754d8273ac03850f13030\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55d4efc081c754d8273ac03850f13030\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78549bff33c42d531fe5485b4de5abb7\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78549bff33c42d531fe5485b4de5abb7\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cc29a7d02877d975ced36548d338d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cc29a7d02877d975ced36548d338d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3494e4d616dcc70de5ac033bfdba3623\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3494e4d616dcc70de5ac033bfdba3623\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c56bd892a6f8979b167a29abc8a282e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c56bd892a6f8979b167a29abc8a282e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:20:9-35
	android:label
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:18:9-41
	android:fullBackupContent
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:19:9-54
	tools:targetApi
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:22:9-29
	android:icon
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:17:9-43
	android:allowBackup
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:14:9-35
	android:theme
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:21:9-47
	android:dataExtractionRules
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:15:9-65
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:13:9-45
activity#com.Hamode.periodpal.MainActivity
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:23:9-33:20
	android:label
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:26:13-45
	android:exported
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:27:13-51
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:24:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:28:13-32:29
action#android.intent.action.MAIN
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:31:17-77
	android:name
		ADDED from D:\PeriodPal\app\src\main\AndroidManifest.xml:31:27-74
uses-sdk
INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml
INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3595878178060218ebb7af3bb6421cb0\transformed\googleid-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3595878178060218ebb7af3bb6421cb0\transformed\googleid-1.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.credentials:credentials:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8b5bc1662136e087738bd938abfe3e7\transformed\credentials-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8b5bc1662136e087738bd938abfe3e7\transformed\credentials-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\462708bd37f95b6500e16ace9e164623\transformed\play-services-fido-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\462708bd37f95b6500e16ace9e164623\transformed\play-services-fido-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5a696deae0fe305f1bc165bd00d7684\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5a696deae0fe305f1bc165bd00d7684\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deeba7ce3690d9da9a220873c8ec344a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\deeba7ce3690d9da9a220873c8ec344a\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d517b2b91accbb1ee438acb3484a3ed3\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d517b2b91accbb1ee438acb3484a3ed3\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2064cea70af18bbf84d40fcf490cc9d9\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2064cea70af18bbf84d40fcf490cc9d9\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9766ed77526c3313f9626574fa94812\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9766ed77526c3313f9626574fa94812\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e728e8aad9a69e3b607399bb48152ee5\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e728e8aad9a69e3b607399bb48152ee5\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89b4dd4a95f1e943837f558e47297d3c\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89b4dd4a95f1e943837f558e47297d3c\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f28034329ea35376057b553db42a06ad\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f28034329ea35376057b553db42a06ad\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f6b594bfcf42b736f6f0ce22e1cffe1\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f6b594bfcf42b736f6f0ce22e1cffe1\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce33304cf3255238ef66c67378667420\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce33304cf3255238ef66c67378667420\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a1bc1d8f37d1a6b0194dab5384fc2c1\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a1bc1d8f37d1a6b0194dab5384fc2c1\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c84a5c0fb8383dc487addc589f49bed\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c84a5c0fb8383dc487addc589f49bed\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\782a0a33dc02496be538bbc661aa65be\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\782a0a33dc02496be538bbc661aa65be\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6d0c3b56b429a19c2f930520d0c251b\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6d0c3b56b429a19c2f930520d0c251b\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a4406fb22280fb302c67f992ef8133c\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a4406fb22280fb302c67f992ef8133c\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\901c18f4ba81f5d50908c9f14f46b683\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\901c18f4ba81f5d50908c9f14f46b683\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4664de8a76f23c3e4e258538ed9b2b58\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4664de8a76f23c3e4e258538ed9b2b58\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc2b5ac2f6eb4aded5cdcf1e07acdfa0\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc2b5ac2f6eb4aded5cdcf1e07acdfa0\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f78b3510f4a1f1bb0a0fdd93d3df1fb2\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f78b3510f4a1f1bb0a0fdd93d3df1fb2\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5912c0c911551901b5253b16937b8db\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5912c0c911551901b5253b16937b8db\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9aad415eee13c5225ceb41d0c3631e4\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9aad415eee13c5225ceb41d0c3631e4\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1790f4c32bdc5aa4409d0db1320fb4f9\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1790f4c32bdc5aa4409d0db1320fb4f9\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cebcbb97b505907ae97ef9f6c573687\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cebcbb97b505907ae97ef9f6c573687\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d5f0b9b8602cbb854f5701accab68e5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d5f0b9b8602cbb854f5701accab68e5\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10298d9d40ee5303b78c26c7400a6920\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10298d9d40ee5303b78c26c7400a6920\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a0ed9c77a7dc9b64c33fef30fe58ae9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a0ed9c77a7dc9b64c33fef30fe58ae9\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b927026c75dd1bea752ac47a44a9de4\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b927026c75dd1bea752ac47a44a9de4\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16198757d7398e736f933a6985e2a1b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16198757d7398e736f933a6985e2a1b\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3c7b3962208464e4594ebb813ca78c2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3c7b3962208464e4594ebb813ca78c2\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0ab38ddb181ba54137f5f1d540c94bd\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0ab38ddb181ba54137f5f1d540c94bd\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f3b0e192fae8d81415bbd16b5199dc2\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f3b0e192fae8d81415bbd16b5199dc2\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f88ef65c9bc8aa67d94848a25d26726\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f88ef65c9bc8aa67d94848a25d26726\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68097891cc9b7c3f82e22792262965b7\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68097891cc9b7c3f82e22792262965b7\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\956eabded315942ce5907cc63dc89883\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\956eabded315942ce5907cc63dc89883\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f318c1dabe4f575f887ee6cb92e13ff\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f318c1dabe4f575f887ee6cb92e13ff\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5faf63cc72e7771e60ff491c35840aa9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5faf63cc72e7771e60ff491c35840aa9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167ac5f57d41a8d32d27023c0237b735\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167ac5f57d41a8d32d27023c0237b735\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a24524cd577336c01efc449f32e7672\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a24524cd577336c01efc449f32e7672\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\738aced92cf0d3e841a31cacc78effce\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\738aced92cf0d3e841a31cacc78effce\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60f3277fff19b86fee9f41dd9f5a0653\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60f3277fff19b86fee9f41dd9f5a0653\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb57a2a816d23d18c0f2c4399312ffc\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8cb57a2a816d23d18c0f2c4399312ffc\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23ccccae73f7acf66d4edaa95c868176\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23ccccae73f7acf66d4edaa95c868176\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14d0d29d49ac4289f3b9055537f4fe1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a14d0d29d49ac4289f3b9055537f4fe1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8d02c1e663e9fd2c30beea09851d0f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8d02c1e663e9fd2c30beea09851d0f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713c3110d19154ee2f68e6a7343e28a7\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\713c3110d19154ee2f68e6a7343e28a7\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aadb35340b4a827d4cbde408ff64707c\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aadb35340b4a827d4cbde408ff64707c\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f623f978dea677cef564465a0f62f1f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f623f978dea677cef564465a0f62f1f\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaef81146e2c476db006c571eca7f901\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaef81146e2c476db006c571eca7f901\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08852f55135dfb4b7aba28a2fcf5d46c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08852f55135dfb4b7aba28a2fcf5d46c\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa00fb55065a2f36fe2f24c84a21863b\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa00fb55065a2f36fe2f24c84a21863b\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a9e4472bfe093db10a5f3627a329c3c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a9e4472bfe093db10a5f3627a329c3c\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a898b8f2723d625fa8143324bd55bcb6\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a898b8f2723d625fa8143324bd55bcb6\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30affbf2ce916a05fd0c246e50a84451\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30affbf2ce916a05fd0c246e50a84451\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca69d028d53405e396434bd1b33a8128\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca69d028d53405e396434bd1b33a8128\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55d4efc081c754d8273ac03850f13030\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55d4efc081c754d8273ac03850f13030\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78549bff33c42d531fe5485b4de5abb7\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78549bff33c42d531fe5485b4de5abb7\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cc29a7d02877d975ced36548d338d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cc29a7d02877d975ced36548d338d6\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1563c945d474f65ddd0bfe2b0b86275f\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1563c945d474f65ddd0bfe2b0b86275f\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54c0ace2928b6b83110c519f8b148f8c\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54c0ace2928b6b83110c519f8b148f8c\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ba1333717fbdc0a7bcd58005c6dff50\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ba1333717fbdc0a7bcd58005c6dff50\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89db37d8cc333b1f09ff0e740265d432\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89db37d8cc333b1f09ff0e740265d432\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87978c6646afe0f4d2724bd00f50f64f\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87978c6646afe0f4d2724bd00f50f64f\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97d602bc9d768a2fbf2c6ec5e195e55c\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97d602bc9d768a2fbf2c6ec5e195e55c\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0654f1c02991675061c1dbffd1cc19c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0654f1c02991675061c1dbffd1cc19c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11cdba141bdfb547960b7fcdc0b4367a\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11cdba141bdfb547960b7fcdc0b4367a\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2565267476792798ff0673441d513549\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2565267476792798ff0673441d513549\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daff2b002bbeb658aea41f8f7c906345\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daff2b002bbeb658aea41f8f7c906345\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1e7285b5cf886f0c3fce641b2bea0f\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce1e7285b5cf886f0c3fce641b2bea0f\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cecf361d2cc0bafaad723890a70f864\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cecf361d2cc0bafaad723890a70f864\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21f60adbff86357bfc9c342c31a6827\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c21f60adbff86357bfc9c342c31a6827\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3494e4d616dcc70de5ac033bfdba3623\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3494e4d616dcc70de5ac033bfdba3623\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad85ee529d468309743a11bec7acaf6d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad85ee529d468309743a11bec7acaf6d\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\812c1db28e1072341150d5f694a46e4e\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\812c1db28e1072341150d5f694a46e4e\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81de88cf9ba06a39513d4b7c0055feb7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81de88cf9ba06a39513d4b7c0055feb7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c56bd892a6f8979b167a29abc8a282e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c56bd892a6f8979b167a29abc8a282e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\481df57a7c0d63c31b36c7697e6872c9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\481df57a7c0d63c31b36c7697e6872c9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7e96ef05eedf52fabeaf4d12b60c517\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7e96ef05eedf52fabeaf4d12b60c517\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c6c8aa082ab40d75baf759016dac52d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c6c8aa082ab40d75baf759016dac52d\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6947564e97ca4142892836bb056425\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6947564e97ca4142892836bb056425\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97bda15f39233236026bb7ae00346b6\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97bda15f39233236026bb7ae00346b6\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddc599e35f50ee5d257f747f663c5956\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddc599e35f50ee5d257f747f663c5956\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\PeriodPal\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
meta-data#com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar
ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:13:17-125
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55d4efc081c754d8273ac03850f13030\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55d4efc081c754d8273ac03850f13030\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74e05d8d0e3ee3dade3057d8b5e6d7c6\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e22e655b94300fc1dbda43e5cee56ea\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3494e4d616dcc70de5ac033bfdba3623\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3494e4d616dcc70de5ac033bfdba3623\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.Hamode.periodpal.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.Hamode.periodpal.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
