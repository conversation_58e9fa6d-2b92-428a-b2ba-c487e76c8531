{"logs": [{"outputFile": "com.Hamode.periodpal.app-mergeDebugResources-63:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2e988808bb7f73a8315e368f7c5f6e3\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "331,427,530,628,726,829,934,10964", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "422,525,623,721,824,929,1041,11060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\11cdba141bdfb547960b7fcdc0b4367a\\transformed\\ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,979,1068,1141,1216,1292,1368,1446,1513", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,974,1063,1136,1211,1287,1363,1441,1508,1631"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1046,1132,3436,3533,3634,4030,4115,10320,10406,10489,10575,10664,10737,10812,10888,11065,11143,11210", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,74,75,75,77,66,122", "endOffsets": "1127,1204,3528,3629,3717,4110,4195,10401,10484,10570,10659,10732,10807,10883,10959,11138,11205,11328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3333,3722,3821,3932", "endColumns": "102,98,110,97", "endOffsets": "3431,3816,3927,4025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9aad415eee13c5225ceb41d0c3631e4\\transformed\\foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11333,11430", "endColumns": "96,94", "endOffsets": "11425,11520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,393,505,604,697,807,937,1061,1202,1288,1388,1479,1577,1695,1811,1916,2043,2167,2295,2447,2570,2688,2812,2933,3025,3124,3236,3369,3465,3583,3690,3816,3950,4060,4158,4239,4333,4427,4534,4620,4703,4808,4888,4975,5074,5176,5270,5374,5460,5561,5659,5762,5879,5959,6069", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "163,275,388,500,599,692,802,932,1056,1197,1283,1383,1474,1572,1690,1806,1911,2038,2162,2290,2442,2565,2683,2807,2928,3020,3119,3231,3364,3460,3578,3685,3811,3945,4055,4153,4234,4328,4422,4529,4615,4698,4803,4883,4970,5069,5171,5265,5369,5455,5556,5654,5757,5874,5954,6064,6170"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4200,4313,4425,4538,4650,4749,4842,4952,5082,5206,5347,5433,5533,5624,5722,5840,5956,6061,6188,6312,6440,6592,6715,6833,6957,7078,7170,7269,7381,7514,7610,7728,7835,7961,8095,8205,8303,8384,8478,8572,8679,8765,8848,8953,9033,9120,9219,9321,9415,9519,9605,9706,9804,9907,10024,10104,10214", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "4308,4420,4533,4645,4744,4837,4947,5077,5201,5342,5428,5528,5619,5717,5835,5951,6056,6183,6307,6435,6587,6710,6828,6952,7073,7165,7264,7376,7509,7605,7723,7830,7956,8090,8200,8298,8379,8473,8567,8674,8760,8843,8948,9028,9115,9214,9316,9410,9514,9600,9701,9799,9902,10019,10099,10209,10315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e8b5bc1662136e087738bd938abfe3e7\\transformed\\credentials-1.3.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,217", "endColumns": "111,113", "endOffsets": "212,326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1209,1315,1462,1585,1692,1828,1952,2071,2308,2452,2557,2704,2826,2966,3117,3181,3249", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "1310,1457,1580,1687,1823,1947,2066,2174,2447,2552,2699,2821,2961,3112,3176,3244,3328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3163db060619b51a72cfb285e5abcba\\transformed\\play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2179", "endColumns": "128", "endOffsets": "2303"}}]}]}