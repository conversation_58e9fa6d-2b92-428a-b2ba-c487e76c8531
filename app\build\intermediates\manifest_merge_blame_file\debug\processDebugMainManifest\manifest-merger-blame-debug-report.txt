1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Hamode.periodpal"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\PeriodPal\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\PeriodPal\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\PeriodPal\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\PeriodPal\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.CAMERA" />
13-->D:\PeriodPal\app\src\main\AndroidManifest.xml:7:5-65
13-->D:\PeriodPal\app\src\main\AndroidManifest.xml:7:22-62
14    <uses-permission
14-->D:\PeriodPal\app\src\main\AndroidManifest.xml:8:5-9:38
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->D:\PeriodPal\app\src\main\AndroidManifest.xml:8:22-77
16        android:maxSdkVersion="32" />
16-->D:\PeriodPal\app\src\main\AndroidManifest.xml:9:9-35
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->D:\PeriodPal\app\src\main\AndroidManifest.xml:10:5-76
17-->D:\PeriodPal\app\src\main\AndroidManifest.xml:10:22-73
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
18-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:22-65
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
19-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
20    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
20-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
20-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
21    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
21-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
21-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
22    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
22-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
22-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
23    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
23-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
23-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="com.Hamode.periodpal.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.Hamode.periodpal.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30
31    <application
31-->D:\PeriodPal\app\src\main\AndroidManifest.xml:12:5-34:19
32        android:name="com.Hamode.periodpal.PeriodPalApplication"
32-->D:\PeriodPal\app\src\main\AndroidManifest.xml:13:9-45
33        android:allowBackup="true"
33-->D:\PeriodPal\app\src\main\AndroidManifest.xml:14:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
35        android:dataExtractionRules="@xml/data_extraction_rules"
35-->D:\PeriodPal\app\src\main\AndroidManifest.xml:15:9-65
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:fullBackupContent="@xml/backup_rules"
38-->D:\PeriodPal\app\src\main\AndroidManifest.xml:16:9-54
39        android:icon="@mipmap/ic_launcher"
39-->D:\PeriodPal\app\src\main\AndroidManifest.xml:17:9-43
40        android:label="@string/app_name"
40-->D:\PeriodPal\app\src\main\AndroidManifest.xml:18:9-41
41        android:roundIcon="@mipmap/ic_launcher_round"
41-->D:\PeriodPal\app\src\main\AndroidManifest.xml:19:9-54
42        android:supportsRtl="true"
42-->D:\PeriodPal\app\src\main\AndroidManifest.xml:20:9-35
43        android:theme="@style/Theme.PeriodPal" >
43-->D:\PeriodPal\app\src\main\AndroidManifest.xml:21:9-47
44        <activity
44-->D:\PeriodPal\app\src\main\AndroidManifest.xml:23:9-33:20
45            android:name="com.Hamode.periodpal.MainActivity"
45-->D:\PeriodPal\app\src\main\AndroidManifest.xml:24:13-41
46            android:exported="true"
46-->D:\PeriodPal\app\src\main\AndroidManifest.xml:25:13-36
47            android:label="@string/app_name"
47-->D:\PeriodPal\app\src\main\AndroidManifest.xml:26:13-45
48            android:theme="@style/Theme.PeriodPal" >
48-->D:\PeriodPal\app\src\main\AndroidManifest.xml:27:13-51
49            <intent-filter>
49-->D:\PeriodPal\app\src\main\AndroidManifest.xml:28:13-32:29
50                <action android:name="android.intent.action.MAIN" />
50-->D:\PeriodPal\app\src\main\AndroidManifest.xml:29:17-69
50-->D:\PeriodPal\app\src\main\AndroidManifest.xml:29:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->D:\PeriodPal\app\src\main\AndroidManifest.xml:31:17-77
52-->D:\PeriodPal\app\src\main\AndroidManifest.xml:31:27-74
53            </intent-filter>
54        </activity>
55
56        <service
56-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
57            android:name="com.google.firebase.components.ComponentDiscoveryService"
57-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
58            android:directBootAware="true"
58-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
59            android:exported="false" >
59-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
60            <meta-data
60-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
61                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
61-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
62                android:value="com.google.firebase.components.ComponentRegistrar" />
62-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
63            <meta-data
63-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
64                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
64-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
65                android:value="com.google.firebase.components.ComponentRegistrar" />
65-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
66            <meta-data
66-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:12:13-14:85
67                android:name="com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar"
67-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:13:17-125
68                android:value="com.google.firebase.components.ComponentRegistrar" />
68-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:14:17-82
69            <meta-data
69-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
70                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
70-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
71                android:value="com.google.firebase.components.ComponentRegistrar" />
71-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
72            <meta-data
72-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
73                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
73-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
74                android:value="com.google.firebase.components.ComponentRegistrar" />
74-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
75            <meta-data
75-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
76                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
76-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
77                android:value="com.google.firebase.components.ComponentRegistrar" />
77-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
78            <meta-data
78-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
79                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
79-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
80                android:value="com.google.firebase.components.ComponentRegistrar" />
80-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
81            <meta-data
81-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
82                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
82-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
83                android:value="com.google.firebase.components.ComponentRegistrar" />
83-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
84            <meta-data
84-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
85                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
85-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
86                android:value="com.google.firebase.components.ComponentRegistrar" />
86-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
87            <meta-data
87-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
88                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
88-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
89                android:value="com.google.firebase.components.ComponentRegistrar" />
89-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
90            <meta-data
90-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
91                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
91-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
93            <meta-data
93-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
94                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
94-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
96            <meta-data
96-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
97                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
97-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
99            <meta-data
99-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
100                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
100-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
102            <meta-data
102-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
103                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
103-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
105            <meta-data
105-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
106                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
106-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
108        </service>
109
110        <activity
110-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
111            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
111-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
112            android:excludeFromRecents="true"
112-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
113            android:exported="true"
113-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
114            android:launchMode="singleTask"
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
115            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
115-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
116            <intent-filter>
116-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
117                <action android:name="android.intent.action.VIEW" />
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
118
119                <category android:name="android.intent.category.DEFAULT" />
119-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
119-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
120                <category android:name="android.intent.category.BROWSABLE" />
120-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
120-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
121
122                <data
122-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
123                    android:host="firebase.auth"
123-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
124                    android:path="/"
124-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
125                    android:scheme="genericidp" />
125-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
126            </intent-filter>
127        </activity>
128        <activity
128-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
129            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
129-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
130            android:excludeFromRecents="true"
130-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
131            android:exported="true"
131-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
132            android:launchMode="singleTask"
132-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
133            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
133-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
134            <intent-filter>
134-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
135                <action android:name="android.intent.action.VIEW" />
135-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
135-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
136
137                <category android:name="android.intent.category.DEFAULT" />
137-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
137-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
138                <category android:name="android.intent.category.BROWSABLE" />
138-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
138-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
139
140                <data
140-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
141                    android:host="firebase.auth"
141-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
142                    android:path="/"
142-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
143                    android:scheme="recaptcha" />
143-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
144            </intent-filter>
145        </activity>
146
147        <service
147-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:24:9-32:19
148            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
148-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:25:13-94
149            android:enabled="true"
149-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:26:13-35
150            android:exported="false" >
150-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:27:13-37
151            <meta-data
151-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:29:13-31:104
152                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
152-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:30:17-76
153                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
153-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:31:17-101
154        </service>
155
156        <activity
156-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:34:9-41:20
157            android:name="androidx.credentials.playservices.HiddenActivity"
157-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:35:13-76
158            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
158-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:36:13-87
159            android:enabled="true"
159-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:37:13-35
160            android:exported="false"
160-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:38:13-37
161            android:fitsSystemWindows="true"
161-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:39:13-45
162            android:theme="@style/Theme.Hidden" >
162-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:40:13-48
163        </activity>
164        <activity
164-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
165            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
165-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
166            android:excludeFromRecents="true"
166-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
167            android:exported="false"
167-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
168            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
168-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
169        <!--
170            Service handling Google Sign-In user revocation. For apps that do not integrate with
171            Google Sign-In, this service will never be started.
172        -->
173        <service
173-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
174            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
174-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
175            android:exported="true"
175-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
176            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
176-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
177            android:visibleToInstantApps="true" />
177-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
178
179        <property
179-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
180            android:name="android.adservices.AD_SERVICES_CONFIG"
180-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
181            android:resource="@xml/ga_ad_services_config" />
181-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
182
183        <provider
183-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
184            android:name="com.google.firebase.provider.FirebaseInitProvider"
184-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
185            android:authorities="com.Hamode.periodpal.firebaseinitprovider"
185-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
186            android:directBootAware="true"
186-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
187            android:exported="false"
187-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
188            android:initOrder="100" />
188-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
189
190        <receiver
190-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
191            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
191-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
192            android:enabled="true"
192-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
193            android:exported="false" >
193-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
194        </receiver>
195
196        <service
196-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
197            android:name="com.google.android.gms.measurement.AppMeasurementService"
197-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
198            android:enabled="true"
198-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
199            android:exported="false" />
199-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
200        <service
200-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
201            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
201-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
202            android:enabled="true"
202-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
203            android:exported="false"
203-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
204            android:permission="android.permission.BIND_JOB_SERVICE" />
204-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
205
206        <activity
206-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
207            android:name="com.google.android.gms.common.api.GoogleApiActivity"
207-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
208            android:exported="false"
208-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
209            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
209-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
210        <activity
210-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
211            android:name="androidx.compose.ui.tooling.PreviewActivity"
211-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
212            android:exported="true" />
212-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
213
214        <provider
214-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
215            android:name="androidx.startup.InitializationProvider"
215-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
216            android:authorities="com.Hamode.periodpal.androidx-startup"
216-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
217            android:exported="false" >
217-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
218            <meta-data
218-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
219                android:name="androidx.emoji2.text.EmojiCompatInitializer"
219-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
220                android:value="androidx.startup" />
220-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
221            <meta-data
221-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
222                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
222-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
223                android:value="androidx.startup" />
223-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
224            <meta-data
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
225                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
226                android:value="androidx.startup" />
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
227        </provider>
228
229        <uses-library
229-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
230            android:name="android.ext.adservices"
230-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
231            android:required="false" />
231-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
232
233        <meta-data
233-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
234            android:name="com.google.android.gms.version"
234-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
235            android:value="@integer/google_play_services_version" />
235-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
236
237        <activity
237-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
238            android:name="androidx.activity.ComponentActivity"
238-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
239            android:exported="true" />
239-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
240
241        <receiver
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
242            android:name="androidx.profileinstaller.ProfileInstallReceiver"
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
243            android:directBootAware="false"
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
244            android:enabled="true"
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
245            android:exported="true"
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
246            android:permission="android.permission.DUMP" >
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
248                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
251                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
252            </intent-filter>
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
254                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
255            </intent-filter>
256            <intent-filter>
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
257                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
258            </intent-filter>
259        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
260        <activity
260-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
261            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
261-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
262            android:exported="false"
262-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
263            android:stateNotNeeded="true"
263-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
264            android:theme="@style/Theme.PlayCore.Transparent" />
264-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
265    </application>
266
267</manifest>
