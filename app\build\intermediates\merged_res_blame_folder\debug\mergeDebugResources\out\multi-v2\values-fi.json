{"logs": [{"outputFile": "com.Hamode.periodpal.app-mergeDebugResources-63:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2e988808bb7f73a8315e368f7c5f6e3\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "334,430,532,630,735,840,952,11188", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "425,527,625,730,835,947,1063,11284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4667,4751,4836,4936,5016,5101,5198,5301,5398,5503,5593,5701,5804,5914,6032,6112,6217", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4662,4746,4831,4931,5011,5096,5193,5296,5393,5498,5588,5696,5799,5909,6027,6107,6212,6311"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4289,4405,4518,4627,4741,4838,4939,5057,5194,5316,5468,5558,5654,5752,5854,5972,6095,6196,6328,6460,6589,6756,6878,7002,7129,7251,7350,7449,7570,7691,7794,7905,8013,8152,8296,8404,8510,8593,8691,8788,8901,8985,9070,9170,9250,9335,9432,9535,9632,9737,9827,9935,10038,10148,10266,10346,10451", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "4400,4513,4622,4736,4833,4934,5052,5189,5311,5463,5553,5649,5747,5849,5967,6090,6191,6323,6455,6584,6751,6873,6997,7124,7246,7345,7444,7565,7686,7789,7900,8008,8147,8291,8399,8505,8588,8686,8783,8896,8980,9065,9165,9245,9330,9427,9530,9627,9732,9822,9930,10033,10143,10261,10341,10446,10545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e8b5bc1662136e087738bd938abfe3e7\\transformed\\credentials-1.3.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,217", "endColumns": "111,116", "endOffsets": "212,329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1247,1358,1511,1642,1748,1891,2017,2133,2390,2531,2637,2786,2912,3060,3199,3265,3335", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "1353,1506,1637,1743,1886,2012,2128,2235,2526,2632,2781,2907,3055,3194,3260,3330,3413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3418,3810,3911,4020", "endColumns": "102,100,108,98", "endOffsets": "3516,3906,4015,4114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3163db060619b51a72cfb285e5abcba\\transformed\\play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2240", "endColumns": "149", "endOffsets": "2385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\11cdba141bdfb547960b7fcdc0b4367a\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,997,1079,1151,1227,1307,1381,1458,1530", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,75,79,73,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,992,1074,1146,1222,1302,1376,1453,1525,1647"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1068,1162,3521,3620,3721,4119,4196,10550,10641,10723,10804,10886,10958,11034,11114,11289,11366,11438", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,75,79,73,76,71,121", "endOffsets": "1157,1242,3615,3716,3805,4191,4284,10636,10718,10799,10881,10953,11029,11109,11183,11361,11433,11555"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9aad415eee13c5225ceb41d0c3631e4\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11560,11650", "endColumns": "89,89", "endOffsets": "11645,11735"}}]}]}